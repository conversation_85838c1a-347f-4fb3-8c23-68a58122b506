# 限购查询页面 - 动态按钮控制实现说明

## 功能需求
根据查询条件 `f_State = '有效'` 来控制按钮的显示：
- 当状态为"有效"时：显示新增按钮和解除限购按钮
- 当状态为"无效"时：隐藏新增按钮和解除限购按钮

## 实现方案

### 1. 前端状态控制
- 使用 `currentQueryCondition` 响应式变量跟踪当前查询状态
- 使用 `showAddButton` 计算属性控制新增按钮显示
- 在 `handleRemove` 函数中检查记录状态

### 2. 按钮控制逻辑
```javascript
// 控制新增按钮
const showAddButton = computed(() => {
  return currentQueryCondition.value.f_State === '有效'
})

// 控制解除限购按钮
function shouldShowRemoveButton(item) {
  const itemState = item['t.f_State'] || item.f_State || item.t_f_State
  return itemState === '有效'
}
```

### 3. 使用方法
1. 页面加载时默认状态为"有效"
2. 用户可以通过测试按钮切换状态来验证功能
3. 实际使用时，状态应该根据查询表单的条件自动更新

### 4. 配置更新
在后端配置中已添加 `f_State` 字段：
```json
{
  "key": "t.f_State",
  "title": "状态",
  "dataModeArray": ["queryForm", "table", "addOrEditForm", "sqlQueryItem", "sqlQueryCondition"]
}
```

### 5. 测试功能
页面顶部有测试控制区域，可以：
- 查看当前查询状态
- 查看新增按钮状态
- 手动切换状态进行测试

## 注意事项
1. XCellList 组件可能不支持所有自定义属性，需要根据实际组件API调整
2. 如果组件不支持动态配置，可能需要修改组件源码或使用其他方法
3. 建议在实际部署前测试所有功能
