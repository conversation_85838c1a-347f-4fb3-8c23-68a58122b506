<script setup lang="ts">
import XForm from '@af-mobile-client-vue3/components/data/XForm/index.vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import { useUserStore } from '@af-mobile-client-vue3/stores/modules/user'
import { useRouter, useRoute } from 'vue-router'
import { reactive, ref, onMounted } from 'vue'

// 获取路由实例
const router = useRouter()
const route = useRoute()

// 访问配置名
const configName = ref('toolSecurity')
// 访问服务名
const serviceName = ref('af-revenue')
// 表单模式
const mode = ref('新增')

const formGroupAddConstruction = ref(null)

// 获取当前用户信息
const userState = useUserStore().getLogin()
const currUser = userState.f

// 预设表单数据，包含用户ID - 借鉴 relieveForm.vue 的写法
const formData = reactive({
  // 从路由参数获取的用户信息ID
  // TODO: 上线时改回 route.query.f_userinfo_id || ''
  f_userinfo_id: route.query.f_userinfo_id || '93172', // 临时写死为93172，上级传过来的用户信息ID

  // 操作员信息字段
  orgId: currUser.resources.orgid || currUser.resources.data?.orgid, // 组织ID
  orgName: currUser.resources.orgs || currUser.resources.data?.orgs, // 组织名称
  depId: currUser.resources.depids || currUser.resources.data?.depids, // 部门ID
  depName: currUser.resources.deps || currUser.resources.data?.deps, // 部门名称
  operator: currUser.resources.name || currUser.resources.data?.name, // 操作员姓名
  operatorId: currUser.resources.id || currUser.resources.data?.id, // 操作员ID

  // 预设表单字段，对应配置中的字段
  f_limit_style: '', // 限购类型
  f_time_type: '', // 限制周期
  f_limit_type: '', // 支付限制
  f_start_date: '', // 执行时间
  f_time_value: '', // 单次限购
  f_third_pay: '', // 支持第三方缴费
  f_limit_times: '', // 限购次数
})

// 🔍 调试：打印初始数据
console.log('=== limitGasQuery.vue 初始化 ===')
console.log('1. 路由参数:', route.query)
console.log('2. 用户信息:', userState.f.resources)
console.log('3. 初始 formData:', formData)

// 组件挂载时设置表单数据
onMounted(() => {
  console.log('=== onMounted 开始执行 ===')

  // 🔍 调试：打印挂载前的数据
  console.log('4. 挂载前 formData:', formData)
  console.log('5. 路由参数详情:', {
    f_userinfo_id: route.query.f_userinfo_id,
    from: route.query.from
  })
  console.log('6. 用户信息详情:', {
    id: userState.f.resources.id,
    name: userState.f.resources.name,
    全部用户信息: userState.f.resources
  })
  console.log('6.1 组织部门信息检查:', {
    'orgid (直接)': userState.f.resources.orgid,
    'orgs (直接)': userState.f.resources.orgs,
    'depids (直接)': userState.f.resources.depids,
    'deps (直接)': userState.f.resources.deps,
    'orgid (data)': userState.f.resources.data?.orgid,
    'orgs (data)': userState.f.resources.data?.orgs,
    'depids (data)': userState.f.resources.data?.depids,
    'deps (data)': userState.f.resources.data?.deps,
    'resources结构': Object.keys(userState.f.resources)
  })

  // 更新表单数据，确保包含从上一个页面传递的信息
  Object.assign(formData, {
    ...formData,
    f_userinfo_id: route.query.f_userinfo_id || '', // 上级传过来的用户信息ID

    // 更新操作员信息字段
    orgId: currUser.resources.orgid || currUser.resources.data?.orgid, // 组织ID
    orgName: currUser.resources.orgs || currUser.resources.data?.orgs, // 组织名称
    depId: currUser.resources.depids || currUser.resources.data?.depids, // 部门ID
    depName: currUser.resources.deps || currUser.resources.data?.deps, // 部门名称
    operator: currUser.resources.name || currUser.resources.data?.name, // 操作员姓名
    operatorId: currUser.resources.id || currUser.resources.data?.id, // 操作员ID
  })

  // 🔍 调试：打印最终设置的数据
  console.log('7. 最终设置的 formData:', formData)
  console.log('8. 数据完整性检查:', {
    'f_userinfo_id 有值': !!formData.f_userinfo_id,
    'f_create_user_id 有值': !!formData.f_create_user_id,
    'f_create_user_name 有值': !!formData.f_create_user_name,
    'f_operate_date 有值': !!formData.f_operate_date
  })
  console.log('=== onMounted 执行完成 ===')
})

// 处理表单提交事件
async function handleSubmit(submitData) {
  console.log('=== 🚀 表单提交开始 ===')
  console.log('9. XForm 传递的原始数据:', submitData)
  console.log('10. 当前 formData 状态:', formData)

  // 🔧 主动获取表单中用户输入的值 - 借鉴 relieveForm.vue 的方式
  const formElements = document.querySelectorAll('textarea, input, select')
  let userLimitStyle = ''
  let userTimeType = ''
  let userLimitType = ''
  let userStartDate = ''
  let userTimeValue = ''
  let userThirdPay = ''
  let userLimitTimes = ''

  formElements.forEach(element => {
    console.log('检查表单元素:', {
      name: element.name,
      value: element.value,
      placeholder: element.placeholder,
      type: element.type
    })

    // 根据字段名或占位符匹配具体字段
    if (element.name === 'f_limit_style' || element.placeholder?.includes('限购类型')) {
      userLimitStyle = element.value
      console.log('10.1 找到限购类型输入框:', element.value)
    }
    if (element.name === 'f_time_type' || element.placeholder?.includes('限制周期')) {
      userTimeType = element.value
      console.log('10.2 找到限制周期输入框:', element.value)
    }
    if (element.name === 'f_limit_type' || element.placeholder?.includes('支付限制')) {
      userLimitType = element.value
      console.log('10.3 找到支付限制输入框:', element.value)
    }
    if (element.name === 'f_start_date' || element.placeholder?.includes('执行时间')) {
      userStartDate = element.value
      console.log('10.4 找到执行时间输入框:', element.value)
    }
    if (element.name === 'f_time_value' || element.placeholder?.includes('单次限购')) {
      userTimeValue = element.value
      console.log('10.5 找到单次限购输入框:', element.value)
    }
    if (element.name === 'f_third_pay' || element.placeholder?.includes('第三方缴费')) {
      userThirdPay = element.value
      console.log('10.6 找到第三方缴费输入框:', element.value)
    }
    if (element.name === 'f_limit_times' || element.placeholder?.includes('限购次数')) {
      userLimitTimes = element.value
      console.log('10.7 找到限购次数输入框:', element.value)
    }
  })

  // 🔧 尝试通过 XForm 引用获取数据
  if (formGroupAddConstruction.value) {
    console.log('10.8 XForm 引用存在，尝试获取表单数据')
    try {
      const xformData = formGroupAddConstruction.value.getFormData?.() || formGroupAddConstruction.value.formData
      console.log('10.9 XForm 内部数据:', xformData)
      if (xformData) {
        userLimitStyle = xformData.f_limit_style || userLimitStyle
        userTimeType = xformData.f_time_type || userTimeType
        userLimitType = xformData.f_limit_type || userLimitType
        userStartDate = xformData.f_start_date || userStartDate
        userTimeValue = xformData.f_time_value || userTimeValue
        userThirdPay = xformData.f_third_pay || userThirdPay
        userLimitTimes = xformData.f_limit_times || userLimitTimes
      }
    } catch (error) {
      console.log('10.10 获取 XForm 数据失败:', error)
    }
  }

  console.log('10.11 检查关键字段:', {
    '限购类型 f_limit_style': submitData.f_limit_style || userLimitStyle,
    '限制周期 f_time_type': submitData.f_time_type || userTimeType,
    '支付限制 f_limit_type': submitData.f_limit_type || userLimitType,
    '执行时间 f_start_date': submitData.f_start_date || userStartDate,
    '单次限购 f_time_value': submitData.f_time_value || userTimeValue,
    '第三方缴费 f_third_pay': submitData.f_third_pay || userThirdPay,
    '限购次数 f_limit_times': submitData.f_limit_times || userLimitTimes,
    '从DOM获取的限购类型': userLimitStyle,
    '从DOM获取的限制周期': userTimeType,
    '从DOM获取的支付限制': userLimitType
  })

  // 🔧 强制更新 formData，确保包含用户输入
  formData.f_limit_style = userLimitStyle || submitData.f_limit_style || ''
  formData.f_time_type = userTimeType || submitData.f_time_type || ''
  formData.f_limit_type = userLimitType || submitData.f_limit_type || ''
  formData.f_start_date = userStartDate || submitData.f_start_date || ''
  formData.f_time_value = userTimeValue || submitData.f_time_value || ''
  formData.f_third_pay = userThirdPay || submitData.f_third_pay || ''
  formData.f_limit_times = userLimitTimes || submitData.f_limit_times || ''

  console.log('10.12 更新后的 formData:', formData)

  // 在提交前确保包含最新的用户信息和所有页面数据 - 借鉴 relieveForm.vue 的写法
  const finalData = {
    ...submitData,
    // 确保包含用户输入的内容
    f_limit_style: submitData.f_limit_style || userLimitStyle || '',
    f_time_type: submitData.f_time_type || userTimeType || '',
    f_limit_type: submitData.f_limit_type || userLimitType || '',
    f_start_date: submitData.f_start_date || userStartDate || '',
    f_time_value: submitData.f_time_value || userTimeValue || '',
    f_third_pay: submitData.f_third_pay || userThirdPay || '',
    f_limit_times: submitData.f_limit_times || userLimitTimes || '',
    // 指定的额外字段
    f_userinfo_id: route.query.f_userinfo_id || formData.f_userinfo_id || '',

    // 操作员信息字段
    orgId: currUser.resources.orgid || currUser.resources.data?.orgid,
    orgName: currUser.resources.orgs || currUser.resources.data?.orgs,
    depId: currUser.resources.depids || currUser.resources.data?.depids,
    depName: currUser.resources.deps || currUser.resources.data?.deps,
    operator: currUser.resources.name || currUser.resources.data?.name, // 操作员姓名（即创建用户名称）
    operatorId: currUser.resources.id || currUser.resources.data?.id, // 操作员ID（即创建用户ID）
    operateDate: new Date().toISOString().slice(0, 19).replace('T', ' '), // 操作时间
  }

  // 🔍 调试：详细打印提交数据
  console.log('11. 最终提交的限购数据:', finalData)
  console.log('12. 数据获取过程检查:', {
    'submitData 原始数据': submitData,
    'formData 初始数据': formData,
    '用户输入的限购类型': userLimitStyle,
    '用户输入的限制周期': userTimeType,
    '用户输入的支付限制': userLimitType
  })
  console.log('13. 提交数据字段检查:', {
    '限购类型 f_limit_style': finalData["f_limit_style"],
    '限制周期 f_time_type': finalData["f_time_type"],
    '支付限制 f_limit_type': finalData["f_limit_type"],
    '执行时间 f_start_date': finalData["f_start_date"],
    '单次限购 f_time_value': finalData["f_time_value"],
    '支持第三方缴费 f_third_pay': finalData["f_third_pay"],
    '限购次数 f_limit_times': finalData["f_limit_times"],
    '操作时间 f_operate_date': finalData["f_operate_date"],
    '用户信息ID f_userinfo_id': finalData.f_userinfo_id,
    '资源用户ID f_resources_id': finalData.f_resources_id,
    '资源用户名称 f_resources_name': finalData.f_resources_name
  })
  console.log('14. 字段值是否为空检查:', {
    '限购类型为空': !finalData["f_limit_style"],
    '限制周期为空': !finalData["f_time_type"],
    '支付限制为空': !finalData["f_limit_type"],
    '执行时间为空': !finalData["f_start_date"],
    '单次限购为空': !finalData["f_time_value"],
    '支持第三方缴费为空': !finalData["f_third_pay"],
    '限购次数为空': !finalData["f_limit_times"]
  })
  console.log('15. 提交地址: http://localhost:9025/limitgas/vv')

  try {
    // 自定义 API 调用，借鉴 relieveForm.vue 的写法
    console.log('16. 开始调用自定义 API...')
    const response = await fetch('http://localhost:9025/limitgas/vv', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(finalData)
    })

    console.log('17. API 响应状态:', response.status)
    console.log('18. API 响应OK:', response.ok)

    if (response.ok) {
      const responseData = await response.json()
      console.log('19. API 响应数据:', responseData)

      // 显示成功提示
      console.log('20. 新增限购成功')

      // 返回上一页
      setTimeout(() => {
        console.log('21. 1秒后将返回上一页')
        router.back()
      }, 1000)

      return responseData
    } else {
      const errorText = await response.text()
      console.error('22. API 调用失败:', errorText)
      throw new Error(`API调用失败，状态码: ${response.status}`)
    }
  } catch (error) {
    console.error('23. 提交过程中发生错误:', error)
    throw error
  }
}

// 返回上一页
function goBack() {
  router.back()
}
</script>

<template>
  <NormalDataLayout id="XFormGroupView" title="新增限购">
    <template #layout_content>
      <XForm
        ref="formGroupAddConstruction"
        :mode="mode"
        :config-name="configName"
        :service-name="serviceName"
        :form-data="formData"
        :submit-button="true"
        :auto-submit="false"
        @on-submit="handleSubmit"
      />
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
.main {
  padding: var(--base-interval-1);

  :deep(.van-search) {
    padding: 0;
  }
}
</style>
