<script setup lang="ts">
import XBadge from '@af-mobile-client-vue3/components/data/XBadge/index.vue'
import XCellListFilter from '@af-mobile-client-vue3/components/data/XCellListFilter/index.vue'
import { getConfigByName, query } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { getRangeByType } from '@af-mobile-client-vue3/utils/queryFormDefaultRangePicker'
import { executeStrFunctionByContext } from '@af-mobile-client-vue3/utils/runEvalFunction'
import LoadError from '@af-mobile-client-vue3/views/common/LoadError.vue'
import {
  showConfirmDialog,
  BackTop as VanBackTop,
  Button as VanButton,
  Col as VanCol,
  Icon as VanIcon,
  List as VanList,
  Popover as VanPopover,
  PullRefresh as Van<PERSON>ullRefresh,
  Row as VanRow,
  Search as VanSearch,
  Space as VanSpace,
  Tag as VanTag,
} from 'vant'
import { computed, defineEmits, defineProps, getCurrentInstance, onBeforeMount, ref, useSlots, watch } from 'vue'
import { useRouter } from 'vue-router'

const {
  configName = '',
  fixQueryForm = null,
  idKey = 'o_id',
  serviceName,
  scanOptions,
  customAdd = false,
  customEdit = false,
  hideAllActions = false,
} = defineProps<{
  configName?: string
  fixQueryForm?: object
  idKey?: string
  serviceName?: string
  scanOptions?: {
    show?: boolean // 是否显示扫码按钮
    type?: string | string[] // 显示类型：可以是单个类型或类型数组 'nfc' / ['scan', 'nfc']
    defaultMode?: string // 默认模式
  }
  // 是否自定义新增、编辑按钮
  customAdd?: boolean
  customEdit?: boolean
  // 是否隐藏所有操作按钮
  hideAllActions?: boolean
}>()

const emit = defineEmits<{
  (e: 'toDetail', item: any): void
  (e: 'update', item: any): void
  (e: 'deleteRow', item: any): void
  (e: 'add'): void
  (e: string, item: any): void
  (e: 'updateCondition', params: any): void
}>()

const userState = useUserStore().getLogin()

const router = useRouter()

const orderVal = ref(undefined)

const sortordVal = ref(undefined)

// 配置内容
const configContent = ref({})

// 表单加载后是否立即执行查询
const isInitQuery = ref(false)

// 主列
const mainColumns = ref([])

// 副标题列
const subTitleColumns = ref([])

// 详情列
const detailColumns = ref([])

// 标签列
const tagList = ref([])

// 标题按钮列
const btnList = ref([])

// 底部列
const footColumns = ref([])

// 所有的复杂操作列
const allActions = ref([])

// 复杂操作列前三项
const mainActions = ref([])

// 复杂操作列其余项
const otherActions = ref([])

// 数据集
const list = ref([])

// 每个 Popover 的显示状态
const showPopover = ref<boolean[]>(list.value.map(() => false))

// 排序集
const orderList = ref([])

// 表单查询数组
const formQueryList = ref([])

// 总数据
const totalCount = ref(0)

// 当前页数
const pageNo = ref(1)

// 每页数量
const pageSize = 20

const searchValue = ref('')

// 数据加载状态
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const isError = ref(false)
const finishedText = ref('加载完成')
// 避免查询多次
const isLastPage = ref(false)

// 条件参数(查询框)
const conditionParams = ref(undefined)

// 主要按钮的状态
const buttonState = ref(undefined)

// 新增or查询的表单配置
const groupFormItems = ref({})
const title = ref('')

// 按钮权限
const buttonPermissions = ref([])

// 默认参数
const defaultParams = {}

const slots = useSlots()

// 当前组件实例（不推荐使用，可能会在后续的版本更迭中调整，暂时用来绑定函数的上下文）
const currInst = getCurrentInstance()

// 列表底部的文字显示
function finishedBottomText() {
  if (!hideAllActions && buttonState.value?.add && buttonState.value.add === true && (filterButtonPermissions('add').state === false || ((filterButtonPermissions('add').state === true && userState.f.resources.f_role_name.includes((filterButtonPermissions('add').roleStr))))))
    return '已加载全部内容，如需新增请点击右上角的 + 号'
  else
    return '已加载全部内容'
}

onBeforeMount(() => {
  initComponent()
})

// 组件初始化
function initComponent() {
  getConfigByName(configName, (result) => {
    groupFormItems.value = result
    title.value = result?.title
    const isQuery = result.createdQuery
    for (let i = 0; i < result.columnJson.length; i++) {
      const item = result.columnJson[i]
      item.span = item.flexSpan
      if (item.slotType === 'badge')
        item.dictName = item.slotKeyMap

      if (item.mobileColumnType === 'mobile_header_column') {
        mainColumns.value.push(item)
      }
      else if (item.mobileColumnType === 'mobile_subtitle_column') {
        subTitleColumns.value.push(item)
      }
      else if (item.mobileColumnType === 'mobile_details_column') {
        detailColumns.value.push(item)
      }
      else if (item.mobileColumnType === 'mobile_footer_column') {
        footColumns.value.push(item)
      }
      else if (item.mobileColumnType === 'mobile_tag_column') {
        tagList.value.push(item)
      }
      else if (item.slotType === 'action' && item.actionArr) {
        for (let j = 0; j < item.actionArr.length; j++) {
          allActions.value.push({
            text: item.actionArr[j].text,
            func: item.actionArr[j].func,
            customFunction: item.actionArr[j].customFunction,
          })
        }
      }

      if (item.btnIcon)
        btnList.value.push(item)

      if (result.showSortIcon && item.sortable) {
        orderList.value.push({
          title: item.title,
          value: item.dataIndex,
        })
      }
    }
    configContent.value = result
    // 扁平化 type=group 的 groupItems，保持顺序
    const flatFormJson = []
    result.formJson.forEach((item) => {
      if (item.type === 'group' && Array.isArray(item.groupItems)) {
        item.groupItems.forEach((groupItem) => {
          if (!groupItem.isOnlyAddOrEdit) {
            flatFormJson.push(groupItem)
          }
        })
      }
      else {
        if (!item.isOnlyAddOrEdit && item.type !== 'group') {
          flatFormJson.push(item)
        }
      }
    })
    formQueryList.value = flatFormJson
    console.log('formQueryList', formQueryList.value)
    if (result.buttonState) {
      buttonState.value = result.buttonState
      buttonPermissions.value = result.buttonPermissions
      if (buttonState.value.edit && buttonState.value.edit === true && (filterButtonPermissions('edit').state === false || ((filterButtonPermissions('edit').state === true && userState.f.resources.f_role_name.includes((filterButtonPermissions('edit').roleStr))))))
        allActions.value.push({ text: '修改', func: 'updateRow' })
      if (buttonState.value.delete && buttonState.value.delete === true && (filterButtonPermissions('delete').state === false || ((filterButtonPermissions('delete').state === true && userState.f.resources.f_role_name.includes((filterButtonPermissions('delete').roleStr))))))
        allActions.value.push({ text: '删除', func: 'deleteRow' })
    }
    splitArrayAt(allActions.value, 3)

    // 初始化条件参数（从表单默认值中获取）
    initConditionParams(result.formJson, isQuery)
  }, serviceName)
}

// 初始化条件参数
function initConditionParams(formItems, isQuery) {
  let hasDefaults: boolean

  // 从表单配置中获取所有默认值
  formItems.forEach((item) => {
    if (item.model) {
      // 根据查询模式获取对应的默认值
      if (item.queryFormDefault !== undefined && item.queryFormDefault !== null) {
        if (item.type === 'rangePicker' && item.queryType === 'BETWEEN') {
          defaultParams[item.model] = getRangeByType(item.queryFormDefault, false)
        }
        else if (['treeSelect', 'select', 'checkbox'].includes(item.type) && ['curOrgId', 'curDepId', 'curUserId'].includes(item.queryFormDefault)) {
          if (item.queryFormDefault === 'curOrgId') {
            defaultParams[item.model] = item.type === 'select' ? userState.f.resources.orgid : [userState.f.resources.orgid]
          }
          if (item.queryFormDefault === 'curDepId') {
            defaultParams[item.model] = item.type === 'select' ? userState.f.resources.depids : [userState.f.resources.depids]
          }
          if (item.queryFormDefault === 'curUserId') {
            defaultParams[item.model] = item.type === 'select' ? userState.f.resources.id : [userState.f.resources.id]
          }
        }
        else {
          defaultParams[item.model] = item.queryFormDefault
        }
      }
    }
  })
  // eslint-disable-next-line prefer-const
  hasDefaults = true

  // 如果有默认值，则设置到条件参数中并立即执行查询
  isInitQuery.value = isQuery
  if (hasDefaults && isInitQuery.value) {
    // 延迟执行第一次查询，确保组件完全加载
    setTimeout(() => {
      loading.value = true
      onLoad(defaultParams)
    }, 100)
  }
}

// 刷新数据
function onRefresh() {
  isError.value = false
  setTimeout(() => {
    // 重新加载数据
    // 将 loading 设置为 true，表示处于加载状态
    refreshing.value = true
    finishedText.value = '加载完成'
    finished.value = false
    loading.value = true
    onLoad(defaultParams)
  }, 100)
}

// 加载数据
function onLoad(defaultParams = {}) {
  if (refreshing.value) {
    list.value = []
    pageNo.value = 1
    isLastPage.value = false
  }
  if (!isLastPage.value) {
    let searchVal = searchValue.value
    if (searchVal === '')
      searchVal = undefined

    // 确保conditionParams不是undefined
    if (conditionParams.value === undefined)
      conditionParams.value = {}
    const mergedParams = mergeParams(defaultParams, conditionParams.value)
    // 输出查询条件，便于调试
    console.log('查询条件:', {
      pageNo: pageNo.value,
      pageSize,
      conditionParams: {
        $queryValue: searchVal,
        ...fixQueryForm,
        ...mergedParams,
      },
    })

    query({
      queryParamsName: configName,
      pageNo: pageNo.value,
      pageSize,
      conditionParams: {
        $queryValue: searchVal,
        ...fixQueryForm,
        ...mergedParams,
      },
      sortField: orderVal?.value,
      sortOrder: sortordVal?.value,
    }, serviceName).then((res: any) => {
      totalCount.value = res.totalCount
      if (res.data.length === 0)
        isLastPage.value = true

      for (const item of res.data)
        list.value.push(item)
      if (list.value.length >= res.totalCount)
        finished.value = true
      else
        pageNo.value = pageNo.value + 1
    }).catch(() => {
      finishedText.value = ''
      finished.value = true
      isError.value = true
    }).finally(() => {
      // 加载状态结束
      loading.value = false
      refreshing.value = false
    })
  }
}

// 合并参数
function mergeParams(defaultParams: object, overrideParams: object) {
  const result = { ...defaultParams }
  for (const [key, value] of Object.entries(overrideParams)) {
    // 只有当overrideParams中的值不是undefined或空字符串时才覆盖
    if (value !== undefined) {
      result[key] = value
    }
  }
  return result
}

// 区分主要操作列与其他操作列
function splitArrayAt<T>(array: T[], index: number) {
  mainActions.value = array.slice(0, index)
  otherActions.value = array.slice(index)
}

// 新增：动态获取按钮分组
function getActionGroups(item: any, index: number) {
  // 先过滤出当前行可见的按钮
  const visibleActions = allActions.value.filter(action =>
    evaluateCustomFunction(action.customFunction, item, index),
  )
  // 前3个为主按钮，其余为更多按钮，保持逆序
  return {
    main: visibleActions.slice(0, 3).reverse(),
    more: visibleActions.slice(3).reverse(),
  }
}

watch(() => searchValue.value, (newVal) => {
  if (newVal === '')
    onRefresh()
})

// 配置中心->表单项展示函数
function handleFunctionStyle(funcString, record) {
  if (!funcString) {
    return {}
  }

  try {
    // 同步执行函数
    const obj = executeStrFunctionByContext(currInst, funcString, [record])
    // 如果返回的是对象，则直接返回
    if (obj && typeof obj === 'object') {
      return obj
    }
    // 其他情况返回空对象
    return {}
  }
  catch (error) {
    console.error('Error in handleFunctionStyle:', error)
    return {}
  }
}

// 逆序排列主要按钮
const reversedMainActions = computed(() => {
  return [...mainActions.value].reverse()
})

// 设置 Popover 的事件
function onSelectMenu(item: any, event: any) {
  if (event.text === '删除') {
    showConfirmDialog({
      title: '删除',
      message:
        '请确认是否删除！！！',
    }).then(() => {
      emit(event.func, item)
    }).catch(() => {
    })
  }
  else if (event.text === '修改') {
    if (customEdit) {
      emit('update', item)
    }
    else {
      // 默认行为 - 导航到XForm页面
      router.push({
        name: 'XForm',
        // params: { id: item },
        query: {
          groupFormItems: JSON.stringify(groupFormItems.value),
          serviceName,
          formData: JSON.stringify(item),
          mode: '修改',
        },
      })
    }
  }
  else {
    emit(event.func, item)
  }
}

// 抛出新增按钮的事件
function addOption() {
  if (customAdd) {
    emit('add')
  }
  else {
    // 默认行为 - 导航到XForm页面
    router.push({
      name: 'XForm',
      query: {
        groupFormItems: JSON.stringify(groupFormItems.value),
        serviceName,
        formData: JSON.stringify({}),
        mode: '新增',
      },
    })
  }
}

// 按钮权限信息筛选
function filterButtonPermissions(btn) {
  return buttonPermissions.value.find(item => item.btnName === btn)
}

// 处理按钮点击
function handleButtonClick(btn, item) {
  emit(`${btn.btnIcon}`, item)
}

// 处理自定义函数
function evaluateCustomFunction(funcString: string | undefined, record: any, index: number): boolean {
  try {
    // 如果 customFunction 不存在，返回 true 表示正常显示
    if (!funcString || funcString === '')
      return true

    // 匹配参数名、函数体
    const innerFuncRegex = /function\s*\((\w+)\s*,\s*(\w+)\)\s*\{([\s\S]*)\}/
    const matches = funcString.match(innerFuncRegex)

    if (!matches)
      return true

    const [, param1, param2, functionBody] = matches

    // eslint-disable-next-line no-new-func
    const func = new Function(param1, param2, functionBody)
    return func(record, index)
  }
  catch (error) {
    console.error('Error evaluating custom function:', error)
    return true
  }
}

/**
 * 函数描述： 传入自定义条件进行查询(传入字段必须在琉璃中配置生成查询项才会生效)
 * @param {string} params - 查询条件map 例: { os_id:1 }
 * // 小提示：此处传入的条件会覆盖掉fixQueryForm(固定查询条件参数)
 */
function updateConditionAndRefresh(params: any) {
  if (params) {
    // 遍历参数，更新对应的表单值
    Object.entries(params).forEach(([key, value]) => {
      // 找到对应的表单项
      conditionParams.value[key] = value
    })
  }

  // 触发刷新
  onRefresh()
}

// 暴露方法给父组件
defineExpose({
  updateConditionAndRefresh,
  onRefresh,
})
</script>

<template>
  <div id="XCellList">
    <VanRow class="filter-condition">
      <!-- 左侧动态插槽区域 -->
      <template v-for="(_, name) in slots" :key="name">
        <template v-if="typeof name === 'string' && name.startsWith('search-left-')">
          <div class="filter-icon-box">
            <slot :name="name" />
          </div>
        </template>
      </template>
      <VanCol class="search-col">
        <VanSearch
          v-model="searchValue"
          class="title-search"
          clearable
          placeholder="综合查询框..."
          shape="round"
          @search="onRefresh"
        />
      </VanCol>
      <!-- 新增按钮，放在查询框后、查询条件下拉按钮前 -->
      <VanCol v-if="!hideAllActions && buttonState?.add && buttonState.add === true && (filterButtonPermissions('add').state === false || ((filterButtonPermissions('add').state === true && userState.f.resources.f_role_name.includes((filterButtonPermissions('add').roleStr)))))" class="add-col">
        <VanButton
          icon="add"
          type="primary"
          size="small"
          class="add-action-btn"
          @click="addOption"
        />
      </VanCol>
      <!-- 右侧动态插槽区域 -->
      <template v-for="(_, name) in slots" :key="name">
        <template v-if="typeof name === 'string' && name.startsWith('search-right-')">
          <div class="filter-icon-box">
            <slot :name="name" />
          </div>
        </template>
      </template>
      <VanCol class="search-icon">
        <XCellListFilter
          v-model:sortord-val="sortordVal"
          v-model:order-val="orderVal"
          v-model:condition-params="conditionParams"
          :fix-query-form="fixQueryForm"
          :service-name="serviceName"
          :order-list="orderList"
          :form-query="formQueryList"
          :button-state="buttonState"
          :button-permissions="buttonPermissions"
          :scan-options="scanOptions"
          @on-refresh="onRefresh"
        />
      </VanCol>
    </VanRow>
    <slot name="search-after" />
    <div class="main">
      <VanPullRefresh v-model="refreshing" :success-text="finishedText" head-height="70" @refresh="onRefresh">
        <template v-if="!isError">
          <VanList
            v-model:loading="loading"
            class="list_main"
            :finished="finished"
            :finished-text="finishedBottomText()"
            :immediate-check="isInitQuery"
            @load="onLoad"
          >
            <div v-for="(item, index) in list" :key="`card_${index}`" class="card_item_main">
              <VanRow gutter="20" class="card_item_header" align="center" @click="emit('toDetail', item)">
                <VanCol :span="24">
                  <div class="title-row">
                    <div v-for="(column) in mainColumns" :key="`main_${column.dataIndex}`" class="main-title">
                      <p
                        class="card_item_title"
                        :style="handleFunctionStyle(column.styleFunctionForValue, item)"
                      >
                        {{ item[column.dataIndex] ?? '--' }}
                      </p>
                    </div>
                    <div v-for="(column) in subTitleColumns" :key="`subtitle_${column.dataIndex}`" class="sub-title">
                      <p
                        class="card_item_subtitle"
                        :style="handleFunctionStyle(column.styleFunctionForValue, item)"
                      >
                        <XBadge
                          :style="handleFunctionStyle(column.styleFunctionForValue, item)"
                          :dict-name="column.dictName" :dict-value="item[column.dataIndex]"
                          :service-name="serviceName"
                        />
                      </p>
                    </div>
                    <div v-if="!hideAllActions" class="action-buttons">
                      <VanButton
                        v-for="btn in btnList"
                        :key="btn.dataIndex"
                        class="action-button"
                        :icon="btn.btnIcon"
                        size="small"
                        @click.stop="handleButtonClick(btn, item)"
                      />
                    </div>
                  </div>
                </VanCol>
              </VanRow>
              <VanRow gutter="20" class="card_item_details" @click="emit('toDetail', item)">
                <VanCol v-for="column of detailColumns" :key="`details_${column.dataIndex}`" :span="column.span">
                  <p>
                    {{ (column.showLabel === undefined || column.showLabel) ? `${column.title}: ` : '' }}
                    <XBadge
                      :style="handleFunctionStyle(column.styleFunctionForValue, item)"
                      :dict-name="column.dictName" :dict-value="item[column.dataIndex]"
                      :service-name="serviceName"
                    />
                  </p>
                </VanCol>
              </VanRow>
              <VanRow v-if="tagList.length > 0" gutter="20" class="tag-row" @click="emit('toDetail', item)">
                <VanCol :span="24">
                  <div class="tag-container">
                    <div class="tag-wrapper">
                      <template
                        v-for="column of tagList"
                        :key="`tag_${column.dataIndex}`"
                      >
                        <VanTag
                          :text-color="column.tagColor"
                          :color="column.tagBorderColor"
                          size="large"
                          class="tag-item"
                        >
                          <div class="tag-content">
                            <VanIcon v-if="column.tagIcon" :name="column.tagIcon" class="tag-icon" />
                            <span v-if="column.showLabel === undefined || column.showLabel" class="tag-title">{{ `${column.title}: ` }}</span>
                            <XBadge
                              :dict-name="column.dictName"
                              :dict-value="item[column.dataIndex]"
                              :service-name="serviceName"
                            />
                          </div>
                        </VanTag>
                      </template>
                    </div>
                  </div>
                </VanCol>
              </VanRow>
              <VanRow
                v-if="footColumns && footColumns.length > 0"
                gutter="20"
                class="card_item_footer"
                @click="emit('toDetail', item)"
              >
                <VanCol v-for="column of footColumns" :key="`foot_${column.dataIndex}`" :span="12">
                  <p>
                    <span :style="handleFunctionStyle(column.styleFunctionForTitle, item)">
                      {{ (column.showLabel === undefined || column.showLabel) ? `${column.title}: ` : '' }}
                    </span>
                    <XBadge
                      :style="handleFunctionStyle(column.styleFunctionForValue, item)"
                      :dict-name="column.dictName" :dict-value="item[column.dataIndex]"
                      :service-name="serviceName"
                    />
                  </p>
                </VanCol>
              </VanRow>
              <!-- 添加详情插槽 -->
              <slot name="item-detail" :item="item" />
              <VanRow v-if="!hideAllActions && allActions.length > 0" gutter="20" class="card_item_bottom">
                <VanCol span="4">
                  <VanPopover
                    v-if="getActionGroups(item, index).more.length"
                    v-model:show="showPopover[index]"
                    placement="bottom-start"
                    theme="dark"
                    overlay
                    :actions="getActionGroups(item, index).more"
                    @select="onSelectMenu(item, $event)"
                  >
                    <template #reference>
                      <div class="more-button">
                        <span>⋯</span>
                      </div>
                    </template>
                  </VanPopover>
                </VanCol>
                <VanCol span="20">
                  <VanRow justify="end">
                    <VanSpace>
                      <VanButton
                        v-for="button in getActionGroups(item, index).main"
                        :key="button.func"
                        type="primary"
                        size="normal"
                        class="action-btn"
                        @click="onSelectMenu(item, button)"
                      >
                        {{ button.text }}
                      </VanButton>
                    </VanSpace>
                  </VanRow>
                </VanCol>
              </VanRow>
            </div>
          </VanList>
        </template>
        <template v-else>
          <LoadError />
        </template>
      </VanPullRefresh>
      <VanBackTop />
    </div>
  </div>
</template>

<style scoped lang="less">
#XCellList {
  height: calc(100vh - var(--van-nav-bar-height) - 20px);
  display: flex;
  flex-direction: column;
  --van-search-padding: 3px;
  --van-dropdown-menu-title-padding: 3px;
  --van-text-color-2: rgb(75, 85, 99);
  --van-button-normal-font-size: 13px;
  .main {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    background-color: var(--van-background);
    padding: var(--van-padding-base) var(--van-padding-sm);

    p {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
    }

    .card_item_main {
      background-color: var(--van-background-2);
      border-radius: var(--van-radius-lg);
      margin: 0 0 var(--van-padding-xs) 0;
      padding: var(--van-padding-sm);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
      transition: all 0.3s ease;
      border: 1px solid rgba(0, 0, 0, 0.04);

      &:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
      }

      .card_item_header {
        margin-bottom: var(--van-padding-base);

        .title-row {
          display: flex;
          align-items: center;
          margin-bottom: 2px;
          width: 100%;

          .main-title {
            display: inline-flex;
            align-items: center;
            .card_item_title {
              font-size: var(--van-font-size-lg);
              font-weight: 700;
              color: var(--van-text-color);
              margin: 0;
            }
          }

          .sub-title {
            display: inline-flex;
            align-items: center;
            margin-left: 8px;
            .card_item_subtitle {
              font-size: var(--van-font-size-md);
              color: var(--van-text-color-2);
            }
          }

          .action-buttons {
            display: flex;
            align-items: center;
            margin-left: auto;
            .action-button {
              margin-left: 6px;
              width: 32px;
              height: 32px;
              padding: 0;
              border: none;
              color: var(--van-primary-color);
              background-color: rgba(25, 137, 250, 0.1);
              border-radius: 6px;
              font-size: var(--van-font-size-lg);
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.2s ease;
              &:active {
                opacity: 0.7;
                background-color: rgba(25, 137, 250, 0.2);
                transform: scale(0.95);
              }
            }
          }
        }
      }

      .tag-row {
        margin-bottom: var(--van-padding-base);
      }

      .tag-container {
        width: 100%;
        .tag-wrapper {
          display: flex;
          flex-wrap: wrap;
          padding: 0 !important;
          align-items: center;
          width: 100%;
          margin: 0 -4px;

          .tag-item {
            width: auto;
            font-size: var(--van-font-size-md);
            margin: 4px 4px;
            :deep(.van-tag) {
              width: fit-content;
              display: inline-flex;
              align-items: center;
              padding: 2px 8px;
              border-radius: 4px;
            }
            .tag-content {
              display: flex;
              align-items: center;
              //white-space: nowrap;
            }
            .tag-icon {
              margin-right: 4px;
              font-size: 14px;
              display: inline-flex;
              align-items: center;
            }
            .tag-title {
              font-weight: normal;
            }
          }
        }
      }

      .card_item_details {
        font-size: var(--van-font-size-md);
        color: var(--van-text-color-2);
        padding: 4px 0;

        .van-col {
          margin-bottom: 4px;
          p {
            display: flex;
            align-items: center;
            gap: 4px;

            span {
              flex: 1;
              min-width: 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            :deep(.van-badge) {
              flex-shrink: 0;
            }
          }
        }
      }

      .card_item_footer {
        font-size: var(--van-font-size-md);
        color: var(--van-text-color-2);
        padding: 4px 0;
        .van-col:last-child {
          text-align: right;
        }
        .van-col:first-child {
          text-align: left;
        }
      }

      .card_item_bottom {
        margin-top: 8px;
        padding-top: 10px;
        border-top: 1px solid rgba(0, 0, 0, 0.06);

        .more-button {
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--van-text-color-2);
          cursor: pointer;
          font-size: var(--van-font-size-lg);
          background-color: var(--van-background);
          border-radius: 6px;
          transition: all 0.2s ease;
          border: 1px solid rgba(0, 0, 0, 0.06);
          span {
            line-height: 1;
            margin-top: -2px;
          }
          &:active {
            opacity: 0.7;
            background-color: var(--van-background-2);
            transform: scale(0.95);
          }
        }

        .action-btn {
          --van-button-primary-border-color: #1890ff;
          --van-button-primary-background: #1890ff;
          min-width: 76px;
          height: 40px;
          border-radius: 10px;
          // font-size: var(--van-font-size-md);
          transition: all 0.2s ease;
          &:active {
            transform: scale(0.95);
          }
        }
      }
    }
  }

  :deep(.van-search__field) {
    padding: 0 !important;
    height: 100%;
  }

  .filter-condition {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #fff;
    gap: 1px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
    position: sticky;
    top: 0;
    z-index: 10;
    .add-col {
      display: flex;
      align-items: center;
      margin: 0 4px;
      .add-action-btn {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background-color: var(--van-background);
        color: #323233;
        border: 1px solid rgba(0, 0, 0, 0.06);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        padding: 0;
        transition: all 0.2s;
        &:hover,
        &:active {
          opacity: 0.85;
          background-color: rgba(25, 137, 250, 0.08);
          border-color: var(--van-primary-color);
          color: var(--van-primary-color);
        }
      }
    }
    :deep(.van-search) {
      width: 100%;
      padding: var(--van-search-padding);
      background-color: transparent;
    }
    :deep(.van-search__content) {
      border-radius: 8px;
      background-color: var(--van-background);
      padding: 4px 12px;
      border: 1px solid rgba(0, 0, 0, 0.01);
      height: 40px;
    }
    :deep(.van-field__left-icon) {
      color: var(--van-text-color-2);
    }
    :deep(.van-cell) {
      background-color: transparent;
    }
    :deep(.van-field__control::placeholder) {
      color: var(--van-text-color-2);
      font-size: 14px;
    }
    .van-col {
      display: flex;
      align-items: center;
      &.search-col {
        flex: 1;
        min-width: 0;
      }
      &.search-icon {
        flex-shrink: 0;
        padding: 0;
      }
    }
    .filter-icon-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 8px;
      background-color: var(--van-background);
      cursor: pointer;
      position: relative;
      z-index: 1;
      border: 1px solid rgba(0, 0, 0, 0.06);
      transition: all 0.2s ease;
      &:active {
        opacity: 0.7;
        transform: scale(0.95);
      }
    }
  }
}
</style>
