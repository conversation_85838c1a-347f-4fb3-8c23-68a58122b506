<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import { showConfirmDialog, showNotify } from 'vant'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 定义事件
// const emit = defineEmits(['deleteRow'])
// 访问路由
const router = useRouter()
// // 获取默认值
const idKey = ref('t_id')

// 简易crud表单测试
const configName = ref('SecuritytoolCRUD')
const serviceName = ref('af-revenue')

// 资源权限测试
// const configName = ref('crud_sources_test')
// const serviceName = ref('af-system')

// 实际业务测试
// const configName = ref('lngChargeAuditMobileCRUD')
// const serviceName = ref('af-gaslink')

// 跳转到详情页面
// function toDetail(item) {
//   router.push({
//     name: 'XCellDetailView',
//     params: { id: item[idKey.value] }, // 如果使用命名路由，推荐使用路由参数而不是直接构建 URL
//     query: {
//       operName: item[operNameKey.value],
//       method:item[methodKey.value],
//       requestMethod:item[requestMethodKey.value],
//       operatorType:item[operatorTypeKey.value],
//       operUrl:item[operUrlKey.value],
//       operIp:item[operIpKey.value],
//       costTime:item[costTimeKey.value],
//       operTime:item[operTimeKey.value],
//
//       title: item[titleKey.value],
//       businessType: item[businessTypeKey.value],
//       status:item[statusKey.value]
//     }
//   })
// }

// // 跳转到表单——以表单组来渲染纯表单
// // 需要在XCellList 中加入@to-detail="toDetail"
// function toDetail(item) {
//   router.push({
//     name: 'XFormGroupView',
//     query: {
//       id: item.t_id,
//       // id: item.rr_id,
//       // o_id: item.o_id,
//     },
//   })
// }

// 自定义新增功能
function addOption() {
  router.push({
    name: 'limit-gas-query',
    query: {
      configName: configName.value,
      serviceName: serviceName.value,
      mode: '新增',
    },
  })
}

// 修改功能
// function updateRow(result) {
//   router.push({
//     name: 'XFormView',
//     params: { id: result.o_id, openid: result.o_id },
//     query: {
//       configName: configName.value,
//       serviceName: serviceName.value,
//       mode: '修改',
//     },
//   })
// }

// // 删除功能
// function deleteRow(result) {
//   emit('deleteRow', result.o_id)
// }

// 解除限购操作 - 对应配置中的 "remove" 函数
function handleRemove(item) {
  console.log('解除限购操作，选中的数据:', item)

  // 检查状态字段，支持多种可能的字段名
  const itemState = item['t.f_state'] || item.f_state || item.t_f_state

  // 只有当状态为"有效"时才允许解除限购
  if (itemState !== '有效') {
    showNotify({
      type: 'warning',
      message: '只有状态为"有效"的记录才能解除限购'
    })
    return
  }

  // 跳转到解除限购表单页面，并传递数据
  router.push({
    name: 'relieve-form',
    query: {
      limitId: item.t_id,
      limitStyle: item.t_f_limit_style || '',
      timeType: item.t_f_time_type || '',
      limitType: item.t_f_limit_type || '',
      from: 'securityCheckResults'
    }
  })
}


</script>

<template>
  <NormalDataLayout id="XCellListView" title="限购查询">
    <template #layout_content>
      <XCellList
        :config-name="configName"
        :service-name="serviceName"
        :custom-add="true"
        :id-key="idKey"
        @add="addOption"
        @remove="handleRemove"
      />
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
</style>
