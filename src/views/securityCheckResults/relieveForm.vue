<script setup lang="ts">
import XForm from '@af-mobile-client-vue3/components/data/XForm/index.vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import { useUserStore } from '@af-mobile-client-vue3/stores/modules/user'
import { useRouter, useRoute } from 'vue-router'
import { reactive, ref, onMounted } from 'vue'

// 获取路由实例
const router = useRouter()
const route = useRoute()

// 获取当前用户信息
const userState = useUserStore().getLogin()
const currUser = userState.f

// 配置信息
const configName = ref('relieveForm') // 解除限购表单配置
const serviceName = ref('af-revenue') // 服务名
const mode = ref('新增') // 表单模式

// XForm 引用
const addWorkLogRef = ref(null)

// 表单数据
const formData = ref({
  // 从路由参数获取的基础信息
  limitId: route.query.limitId || '',
  limitStyle: route.query.limitStyle || '',
  timeType: route.query.timeType || '',
  limitType: route.query.limitType || '',

  // 解除操作相关信息
  remove: '', // 解除原因（对应配置中的字段）
  f_void_remarks: '', // 其他原因（对应配置中的字段）

  // 操作员信息字段
  orgId: currUser.resources.orgid || currUser.resources.data?.orgid, // 组织ID
  orgName: currUser.resources.orgs || currUser.resources.data?.orgs, // 组织名称
  depId: currUser.resources.depids || currUser.resources.data?.depids, // 部门ID
  depName: currUser.resources.deps || currUser.resources.data?.deps, // 部门名称
  operator: currUser.resources.name || currUser.resources.data?.name, // 操作员姓名
  operatorId: currUser.resources.id || currUser.resources.data?.id, // 操作员ID
})

// 🔍 调试：打印初始数据
console.log('=== relieveForm.vue 初始化 ===')
console.log('1. 路由参数:', route.query)
console.log('2. 用户信息:', userState.f.resources)
console.log('3. 初始 formData:', formData.value)



// 组件挂载时设置表单数据
onMounted(() => {
  console.log('=== onMounted 开始执行 ===')

  // 🔍 调试：打印挂载前的数据
  console.log('4. 挂载前 formData:', formData.value)
  console.log('5. 路由参数详情:', {
    limitId: route.query.limitId,
    limitStyle: route.query.limitStyle,
    timeType: route.query.timeType,
    limitType: route.query.limitType,
    from: route.query.from
  })
  console.log('6. 用户信息详情:', {
    id: userState.f.resources.id,
    name: userState.f.resources.name,
    全部用户信息: userState.f.resources
  })
  console.log('6.1 组织部门信息检查:', {
    'orgid (直接)': userState.f.resources.orgid,
    'orgs (直接)': userState.f.resources.orgs,
    'depids (直接)': userState.f.resources.depids,
    'deps (直接)': userState.f.resources.deps,
    'orgid (data)': userState.f.resources.data?.orgid,
    'orgs (data)': userState.f.resources.data?.orgs,
    'depids (data)': userState.f.resources.data?.depids,
    'deps (data)': userState.f.resources.data?.deps,
    'resources结构': Object.keys(userState.f.resources)
  })

  // 设置表单数据，包含从上一个页面传递的信息和当前用户信息
  formData.value = {
    ...formData.value,
    // 解除操作的关键信息
    f_limit_id: route.query.limitId || '', // 限购记录ID

    // 新配置中的字段
    remove: '', // 解除原因
    f_void_remarks: '', // 其他原因

    // 显示信息（只读）
    f_limit_style: route.query.limitStyle || '', // 限购类型
    f_time_type: route.query.timeType || '', // 限制周期
    f_limit_type: route.query.limitType || '', // 支付限制

    // 更新操作员信息字段
    orgId: currUser.resources.orgid || currUser.resources.data?.orgid, // 组织ID
    orgName: currUser.resources.orgs || currUser.resources.data?.orgs, // 组织名称
    depId: currUser.resources.depids || currUser.resources.data?.depids, // 部门ID
    depName: currUser.resources.deps || currUser.resources.data?.deps, // 部门名称
    operator: currUser.resources.name || currUser.resources.data?.name, // 操作员姓名
    operatorId: currUser.resources.id || currUser.resources.data?.id, // 操作员ID
  }

  // 🔍 调试：打印最终设置的数据
  console.log('7. 最终设置的 formData:', formData.value)
  console.log('8. 数据完整性检查:', {
    'f_limit_id 有值': !!formData.value.f_limit_id,
    'f_modify_user_id 有值': !!formData.value.f_modify_user_id,
    'f_modify_user_name 有值': !!formData.value.f_modify_user_name,
    'f_modify_date 有值': !!formData.value.f_modify_date,
    'f_limit_style 有值': !!formData.value.f_limit_style,
    'f_time_type 有值': !!formData.value.f_time_type,
    'f_limit_type 有值': !!formData.value.f_limit_type
  })
  console.log('=== onMounted 执行完成 ===')
})

// 处理表单提交
async function submit(submitData) {
  console.log('=== 🚀 表单提交开始 ===')
  console.log('9. XForm 传递的原始数据:', submitData)
  console.log('10. 当前 formData 状态:', formData.value)

  // 🔧 主动获取表单中用户输入的值
  const formElements = document.querySelectorAll('textarea, input, select')
  let userInputReason = ''
  let userInputVoidRemarks = ''

  formElements.forEach(element => {
    console.log('检查表单元素:', {
      name: element.name,
      value: element.value,
      placeholder: element.placeholder,
      type: element.type
    })

    if (element.name === 'remove' || element.placeholder?.includes('解除原因')) {
      userInputReason = element.value
      console.log('10.1 找到解除原因选择框:', element.value)
    }
    if (element.name === 'f_void_remarks' || element.placeholder?.includes('其他原因')) {
      userInputVoidRemarks = element.value
      console.log('10.2 找到其他原因输入框:', element.value)
    }
  })

  // 🔧 尝试通过 XForm 引用获取数据
  if (addWorkLogRef.value) {
    console.log('10.3 XForm 引用存在，尝试获取表单数据')
    try {
      const xformData = addWorkLogRef.value.getFormData?.() || addWorkLogRef.value.formData
      console.log('10.4 XForm 内部数据:', xformData)
      if (xformData) {
        userInputReason = xformData.remove || userInputReason
        userInputVoidRemarks = xformData.f_void_remarks || userInputVoidRemarks
      }
    } catch (error) {
      console.log('10.5 获取 XForm 数据失败:', error)
    }
  }

  console.log('10.6 检查关键字段:', {
    '解除原因 remove': submitData.remove || userInputReason,
    '其他原因 f_void_remarks': submitData.f_void_remarks || userInputVoidRemarks,
    '从DOM获取的解除原因': userInputReason,
    '从DOM获取的其他原因': userInputVoidRemarks
  })

  // 🔧 强制更新 formData，确保包含用户输入
  formData.value = {
    ...formData.value,
    remove: userInputReason || submitData.remove || '',
    f_void_remarks: userInputVoidRemarks || submitData.f_void_remarks || ''
  }
  console.log('10.7 更新后的 formData:', formData.value)

  // 在提交前确保包含最新的修改人和修改时间，以及用户输入的内容
  const finalData = {
    ...submitData,
    // 确保包含用户输入的内容
    remove: submitData.remove || userInputReason || '',
    f_void_remarks: submitData.f_void_remarks || userInputVoidRemarks || '',

    // 操作员信息字段（包含修改人信息）
    orgId: currUser.resources.orgid || currUser.resources.data?.orgid,
    orgName: currUser.resources.orgs || currUser.resources.data?.orgs,
    depId: currUser.resources.depids || currUser.resources.data?.depids,
    depName: currUser.resources.deps || currUser.resources.data?.deps,
    operator: currUser.resources.name || currUser.resources.data?.name, // 操作员姓名（即修改人姓名）
    operatorId: currUser.resources.id || currUser.resources.data?.id, // 操作员ID（即修改人ID）
    operateDate: new Date().toISOString().slice(0, 19).replace('T', ' '), // 操作时间（即修改时间）
  }

  // 🔍 调试：详细打印提交数据
  console.log('11. 最终提交的解除限购数据:', finalData)
  console.log('12. 提交数据字段检查:', {
    '限购记录ID': finalData.f_limit_id,
    '解除原因': finalData.remove,
    '其他原因': finalData.f_void_remarks,
    '修改人ID': finalData.f_modify_user_id,
    '修改人姓名': finalData.f_modify_user_name,
    '修改时间': finalData.f_modify_date,
    '限购类型': finalData.f_limit_style,
    '限制周期': finalData.f_time_type,
    '支付限制': finalData.f_limit_type
  })
  console.log('13. 提交地址: http://localhost:9025/limitgas/cc1')

  try {
    // 自定义 API 调用，不使用 XForm 的默认提交
    console.log('14. 开始调用自定义 API...')
    const response = await fetch('http://localhost:9025/limitgas/cc1', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(finalData)
    })

    console.log('15. API 响应状态:', response.status)
    console.log('16. API 响应OK:', response.ok)

    if (response.ok) {
      const responseData = await response.json()
      console.log('17. API 响应数据:', responseData)

      // 显示成功提示
      console.log('18. 解除限购成功')

      // 返回上一页
      setTimeout(() => {
        console.log('19. 1秒后将返回上一页')
        router.back()
      }, 1000)

      return responseData
    } else {
      const errorText = await response.text()
      console.error('20. API 调用失败:', errorText)
      throw new Error(`API调用失败，状态码: ${response.status}`)
    }
  } catch (error) {
    console.error('21. 提交过程中发生错误:', error)
    throw error
  }
}



// 返回上一页
function goBack() {
  router.back()
}
</script>

<template>
  <NormalDataLayout id="RelieveFormView" title="解除限购">
    <template #layout_content>
      <XForm
        ref="addWorkLogRef"
        :mode="mode"
        :config-name="configName"
        :service-name="serviceName"
        :form-data="formData"
        :submit-button="true"
        :auto-submit="false"
        @on-submit="submit"
      />
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
.main {
  padding: var(--base-interval-1);

  :deep(.van-search) {
    padding: 0;
  }
}
</style>
